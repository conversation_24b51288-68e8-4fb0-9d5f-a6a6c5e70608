#!/usr/bin/env python3
"""
Empty Author Name Extractor Script

Reads CSV files containing 'Author Name', 'Email', 'Article Title' columns
and extracts rows where 'Author Name' is empty to a separate folder.

Author: AI Assistant
Date: 2026-01-22
"""

import os
import glob
import pandas as pd
import warnings
from datetime import datetime

# Import rich_progress for gradient progress bars
import rich_progress

# Suppress pandas warnings
try:
    from pandas.core.common import SettingWithCopyWarning
    warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
except ImportError:
    try:
        from pandas.errors import SettingWithCopyWarning
        warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
    except (ImportError, AttributeError):
        warnings.filterwarnings("ignore", message=".*SettingWithCopyWarning.*")

def is_empty_value(value):
    """Check if a value is considered empty (NaN, None, empty string, or whitespace only)."""
    if pd.isna(value):
        return True
    if value is None:
        return True
    if isinstance(value, str) and value.strip() == "":
        return True
    return False

def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 60, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

# Print welcome header
print_header("Empty Author Name Extractor Tool")

# Get the input folder path from the user
print_section("Input Folder")
input_folder = input("Enter the folder path containing CSV files: ").strip()

if not input_folder:
    rich_progress.print_status("No folder path provided. Exiting.", "error")
    exit()

if not os.path.exists(input_folder):
    rich_progress.print_status(f"Folder '{input_folder}' does not exist. Exiting.", "error")
    exit()

if not os.path.isdir(input_folder):
    rich_progress.print_status(f"'{input_folder}' is not a directory. Exiting.", "error")
    exit()

rich_progress.print_status(f"Input folder: {input_folder}", "info")

# Find all CSV files in the input folder
print_section("Finding CSV Files")
csv_pattern = os.path.join(input_folder, "*.csv")
csv_files = glob.glob(csv_pattern)

if not csv_files:
    rich_progress.print_status("No CSV files found in the specified folder.", "error")
    exit()

rich_progress.print_status(f"Found {len(csv_files)} CSV files", "success")
for csv_file in csv_files:
    rich_progress.print_status(f"  - {os.path.basename(csv_file)}", "info")

# Create output folder
print_section("Creating Output Folder")
output_folder = os.path.join(input_folder, "empty_author_names")
os.makedirs(output_folder, exist_ok=True)
rich_progress.print_status(f"Output folder created: {output_folder}", "success")

# Process each CSV file
print_section("Processing CSV Files")

# Create a progress bar for processing files
process_bar, update_process = rich_progress.create_progress_bar(
    total=len(csv_files),
    description="Processing CSV files",
    color_scheme="blue"
)

total_rows_processed = 0
total_empty_author_rows = 0
processed_files = 0
error_files = 0

for csv_file in csv_files:
    try:
        # Read the CSV file
        df = pd.read_csv(csv_file, low_memory=False)
        filename = os.path.basename(csv_file)
        
        # Check if required columns exist
        required_columns = ['Author Name', 'Email', 'Article Title']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            rich_progress.print_status(f"Warning: {filename} missing columns: {missing_columns}", "warning")
            update_process(1, f"Skipped {filename} (missing columns)")
            error_files += 1
            continue
        
        # Filter rows where 'Author Name' is empty
        empty_author_mask = df['Author Name'].apply(is_empty_value)
        empty_author_df = df[empty_author_mask].copy()
        
        rows_in_file = len(df)
        empty_rows_in_file = len(empty_author_df)
        
        total_rows_processed += rows_in_file
        total_empty_author_rows += empty_rows_in_file
        
        # Save the filtered data if there are any empty author rows
        if empty_rows_in_file > 0:
            # Create output filename
            base_name = os.path.splitext(filename)[0]
            output_filename = f"{base_name}_empty_authors.csv"
            output_path = os.path.join(output_folder, output_filename)
            
            # Save to CSV with UTF-8-BOM encoding
            empty_author_df.to_csv(output_path, encoding='utf-8-sig', index=False)
            
            update_process(1, f"Processed {filename} ({empty_rows_in_file}/{rows_in_file} empty authors)")
        else:
            update_process(1, f"Processed {filename} (no empty authors)")
        
        processed_files += 1
        
    except Exception as e:
        rich_progress.print_status(f"Error processing {filename}: {str(e)}", "error")
        update_process(1, f"Error with {filename}")
        error_files += 1

# Stop the progress bar
process_bar.stop()

# Print completion summary
print_header("Processing Completed!")
rich_progress.print_status(f"Input folder: {input_folder}", "info")
rich_progress.print_status(f"Output folder: {output_folder}", "info")
rich_progress.print_status(f"CSV files found: {len(csv_files)}", "info")
rich_progress.print_status(f"Files successfully processed: {processed_files}", "success")
rich_progress.print_status(f"Files with errors: {error_files}", "error" if error_files > 0 else "info")
rich_progress.print_status(f"Total rows processed: {total_rows_processed:,}", "info")
rich_progress.print_status(f"Rows with empty Author Name: {total_empty_author_rows:,}", "success")

if total_empty_author_rows > 0:
    percentage = (total_empty_author_rows / total_rows_processed) * 100
    rich_progress.print_status(f"Percentage of empty authors: {percentage:.2f}%", "info")
    rich_progress.print_status("Files with empty author names have been saved to the 'empty_author_names' folder", "success")
else:
    rich_progress.print_status("No rows with empty Author Name found in any files", "info")
